# Website Developer 0002 - E-commerce Platform

A complete PHP-based e-commerce platform for selling web development services with integrated payment gateway, order management, and admin dashboard.

## 🚀 Features

### Core Features
- **Service Management**: Create, edit, and manage web development service packages
- **User Authentication**: Secure login/registration system with role-based access
- **Order Management**: Complete order lifecycle from placement to delivery
- **Payment Integration**: Razorpay payment gateway with secure transactions
- **Admin Dashboard**: Comprehensive admin panel with analytics and reporting
- **Client Dashboard**: User-friendly client portal for order tracking
- **Responsive Design**: Mobile-first responsive design using Bootstrap 5

### Advanced Features
- **Real-time Order Tracking**: Milestone-based progress tracking
- **Invoice Generation**: Automated PDF invoice generation
- **Email Notifications**: Automated email notifications for order updates
- **Search & Filtering**: Advanced search and filtering for services
- **File Upload**: Secure file upload for project requirements
- **Analytics**: Revenue analytics and reporting dashboard
- **Multi-language Support**: Hindi/Hinglish content support

## 🛠️ Technology Stack

- **Backend**: PHP 8+, MySQL 8+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), Bootstrap 5
- **Payment Gateway**: Razorpay
- **Database**: MySQL with PDO
- **Server**: Apache (XAMPP compatible)
- **Libraries**: Font Awesome, Chart.js, jQuery

## 📋 Requirements

- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache Web Server
- XAMPP (recommended for local development)
- Composer (optional, for future dependencies)

## 🔧 Installation

### 1. Clone/Download the Project
```bash
# If using Git
git clone https://github.com/yourusername/websitedeveloper0002.git

# Or download and extract to your XAMPP htdocs folder
# Path: C:\xampp\htdocs\websitedeveloper0002.in\
```

### 2. Database Setup
1. Start XAMPP and ensure MySQL is running
2. Open phpMyAdmin (http://localhost/phpmyadmin)
3. Create a new database named `webdev_ecommerce`
4. Import the database schema:
   ```sql
   # Run the SQL file: database/schema.sql
   ```

### 3. Configuration
1. Open `config/database.php`
2. Update database credentials if needed:
   ```php
   private $host = 'localhost';
   private $db_name = 'webdev_ecommerce';
   private $username = 'root';
   private $password = '';
   ```

3. Configure Razorpay settings:
   ```php
   const RAZORPAY_KEY_ID = 'your-razorpay-key-id';
   const RAZORPAY_KEY_SECRET = 'your-razorpay-key-secret';
   const RAZORPAY_WEBHOOK_SECRET = 'your-webhook-secret';
   ```

### 4. File Permissions
Ensure the following directories are writable:
- `uploads/`
- `assets/uploads/`

### 5. Access the Application
- **Frontend**: http://localhost/websitedeveloper0002.in/
- **Admin Panel**: http://localhost/websitedeveloper0002.in/?page=admin

## 👤 Default Admin Account

After running the database schema, you can create an admin account:

```sql
INSERT INTO users (full_name, email, password, role, status) 
VALUES ('Admin User', '<EMAIL>', '$2y$10$hash_here', 'admin', 'active');
```

Or register normally and update the role in the database.

## 🎯 Usage Guide

### For Clients
1. **Browse Services**: View available web development packages
2. **Place Order**: Select a service and provide requirements
3. **Make Payment**: Secure payment via Razorpay
4. **Track Progress**: Monitor order status in dashboard
5. **Download Invoice**: Get payment receipts

### For Admins
1. **Manage Services**: Add/edit service packages
2. **Process Orders**: Update order status and milestones
3. **View Analytics**: Monitor revenue and performance
4. **Manage Users**: Handle client accounts
5. **Generate Reports**: Export sales and order reports

## 🔐 Security Features

- **Password Hashing**: Secure password storage using PHP's password_hash()
- **SQL Injection Protection**: PDO prepared statements
- **XSS Prevention**: Input sanitization and output escaping
- **CSRF Protection**: Form token validation
- **File Upload Security**: Type and size validation
- **Session Management**: Secure session handling

## 📱 API Endpoints

### Authentication
- `POST /index.php` - Login/Register (AJAX)
- `GET /logout.php` - User logout

### Orders
- `GET /index.php?page=orders` - View orders
- `POST /index.php` - Create order (AJAX)
- `GET /order-details.php?id=` - Order details

### Payments
- `POST /payment-verify.php` - Verify Razorpay payment
- `GET /invoice.php?order=` - Download invoice

## 🎨 Customization

### Styling
- Main CSS: `assets/css/style.css`
- Custom variables available for easy theming
- Bootstrap 5 classes for rapid development

### Adding New Features
1. Create new model in `models/`
2. Add corresponding pages in `pages/`
3. Update routing in `index.php`
4. Add database tables if needed

## 🚀 Deployment

### Production Setup
1. Upload files to web server
2. Update database credentials
3. Set up SSL certificate
4. Configure Razorpay production keys
5. Set proper file permissions
6. Enable error logging

### Environment Variables
Consider using environment variables for sensitive data:
- Database credentials
- Payment gateway keys
- Email configuration

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection Error**
   - Check MySQL service is running
   - Verify database credentials
   - Ensure database exists

2. **Payment Issues**
   - Verify Razorpay credentials
   - Check webhook configuration
   - Review error logs

3. **File Upload Problems**
   - Check directory permissions
   - Verify file size limits
   - Review allowed file types

### Debug Mode
Enable debug mode by adding to `config/database.php`:
```php
const DEBUG_MODE = true;
```

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Phone**: +91 98765 43210
- **WhatsApp**: +91 98765 43210

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📈 Roadmap

- [ ] Multi-language support
- [ ] Advanced analytics
- [ ] Mobile app integration
- [ ] Subscription services
- [ ] Advanced reporting
- [ ] Integration with more payment gateways

---

**Website Developer 0002** - Professional Web Development Services Platform
