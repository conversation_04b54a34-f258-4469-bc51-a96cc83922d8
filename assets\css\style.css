/* Website Developer 0002 - Custom Styles */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: #333;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* Main Content */
.main-content {
    margin-top: 76px; /* Account for fixed navbar */
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.card-img-top {
    border-radius: 12px 12px 0 0;
}

/* Service Cards */
.service-card .card {
    height: 100%;
    overflow: hidden;
}

.service-card .card-body {
    padding: 1.5rem;
}

.service-card .price {
    font-size: 1.2rem;
}

/* Feature Boxes */
.feature-box {
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-10px);
}

.feature-icon {
    margin-bottom: 1.5rem;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stat-item {
    padding: 2rem 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-features .fas {
    font-size: 1.2rem;
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 2px solid #e9ecef;
    border-right: none;
    background: #f8f9fa;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
        text-align: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .feature-box {
        padding: 1.5rem 1rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
    }
}

@media (max-width: 576px) {
    .main-content {
        margin-top: 70px;
    }
    
    .hero-section {
        padding: 3rem 0;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

/* Utility Classes */
.text-purple {
    color: #6f42c1 !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-soft {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.border-radius-lg {
    border-radius: 12px;
}

.min-vh-75 {
    min-height: 75vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
