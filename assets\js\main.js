/**
 * Website Developer 0002 - Main JavaScript
 */

// Global variables
let currentUser = null;
let cart = [];

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize order now buttons
    initializeOrderButtons();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize animations
    initializeAnimations();
    
    // Load cart from localStorage
    loadCart();
}

// Initialize order buttons
function initializeOrderButtons() {
    const orderButtons = document.querySelectorAll('.order-now-btn');
    
    orderButtons.forEach(button => {
        button.addEventListener('click', function() {
            const packageId = this.getAttribute('data-package-id');
            handleOrderNow(packageId);
        });
    });
}

// Handle order now button click
function handleOrderNow(packageId) {
    // Check if user is logged in
    if (!isLoggedIn()) {
        showAlert('warning', 'Please login to place an order.');
        // Show login modal
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
        return;
    }
    
    // Get package details
    getPackageDetails(packageId).then(package => {
        if (package) {
            // Redirect to checkout with package ID
            window.location.href = `?page=checkout&package=${packageId}`;
        } else {
            showAlert('danger', 'Package not found.');
        }
    });
}

// Get package details via AJAX
function getPackageDetails(packageId) {
    return fetch('index.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=get_package&package_id=${packageId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data.package;
        }
        return null;
    })
    .catch(error => {
        console.error('Error:', error);
        return null;
    });
}

// Check if user is logged in
function isLoggedIn() {
    // This would be set by PHP when page loads
    return typeof userLoggedIn !== 'undefined' && userLoggedIn;
}

// Show alert message
function showAlert(type, message, duration = 5000) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-floating');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-floating`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    alertDiv.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto remove after duration
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

// Get alert icon based on type
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Initialize search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 500);
            }
        });
    }
}

// Perform search
function performSearch(query) {
    // Show loading state
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
        searchResults.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
    }
    
    // Perform AJAX search
    fetch('index.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=search&query=${encodeURIComponent(query)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && searchResults) {
            displaySearchResults(data.results);
        }
    })
    .catch(error => {
        console.error('Search error:', error);
        if (searchResults) {
            searchResults.innerHTML = '<div class="text-center text-muted">Search failed. Please try again.</div>';
        }
    });
}

// Display search results
function displaySearchResults(results) {
    const searchResults = document.getElementById('searchResults');
    if (!searchResults) return;
    
    if (results.length === 0) {
        searchResults.innerHTML = '<div class="text-center text-muted">No results found.</div>';
        return;
    }
    
    let html = '<div class="row">';
    results.forEach(result => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">${result.name}</h6>
                        <p class="card-text small text-muted">${result.short_description}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">₹${result.price}</span>
                            <a href="?page=service&slug=${result.slug}" class="btn btn-sm btn-outline-primary">View</a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    searchResults.innerHTML = html;
}

// Initialize animations
function initializeAnimations() {
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements with animation class
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach(el => observer.observe(el));
}

// Cart functionality
function loadCart() {
    const savedCart = localStorage.getItem('webdev_cart');
    if (savedCart) {
        cart = JSON.parse(savedCart);
        updateCartUI();
    }
}

function saveCart() {
    localStorage.setItem('webdev_cart', JSON.stringify(cart));
}

function addToCart(packageId, packageData) {
    // Check if item already in cart
    const existingItem = cart.find(item => item.id === packageId);
    
    if (existingItem) {
        showAlert('info', 'Item already in cart');
        return;
    }
    
    cart.push({
        id: packageId,
        name: packageData.name,
        price: packageData.price,
        image: packageData.image
    });
    
    saveCart();
    updateCartUI();
    showAlert('success', 'Item added to cart');
}

function removeFromCart(packageId) {
    cart = cart.filter(item => item.id !== packageId);
    saveCart();
    updateCartUI();
    showAlert('info', 'Item removed from cart');
}

function updateCartUI() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = cart.length;
        cartCount.style.display = cart.length > 0 ? 'inline' : 'none';
    }
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(amount);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Form validation helpers
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validatePhone(phone) {
    const re = /^[\+]?[1-9][\d]{0,15}$/;
    return re.test(phone.replace(/\s/g, ''));
}

// Loading state helpers
function showLoading(element) {
    element.classList.add('loading');
    element.style.pointerEvents = 'none';
}

function hideLoading(element) {
    element.classList.remove('loading');
    element.style.pointerEvents = 'auto';
}

// Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('success', 'Copied to clipboard');
    }).catch(() => {
        showAlert('danger', 'Failed to copy');
    });
}

// Initialize tooltips and popovers
function initializeBootstrapComponents() {
    // Tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Call initialization
initializeBootstrapComponents();
