<?php
/**
 * Database Configuration
 * Website Developer E-commerce Platform
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'webdev_ecommerce';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

/**
 * Application Configuration
 */
class Config {
    // Site Configuration
    const SITE_NAME = 'Website Developer 0002';
    const SITE_URL = 'http://localhost/websitedeveloper0002.in';
    const SITE_EMAIL = '<EMAIL>';
    
    // Security
    const SECRET_KEY = 'your-secret-key-here-change-in-production';
    const JWT_SECRET = 'jwt-secret-key-change-in-production';
    
    // File Upload
    const UPLOAD_PATH = 'uploads/';
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'zip'];
    
    // Pagination
    const ITEMS_PER_PAGE = 12;
    
    // Email Configuration (for notifications)
    const SMTP_HOST = 'smtp.gmail.com';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '';
    const SMTP_PASSWORD = '';
    
    // Razorpay Configuration
    const RAZORPAY_KEY_ID = 'your-razorpay-key-id';
    const RAZORPAY_KEY_SECRET = 'your-razorpay-key-secret';
    const RAZORPAY_WEBHOOK_SECRET = 'your-webhook-secret';
    
    // Currency
    const CURRENCY = 'INR';
    const CURRENCY_SYMBOL = '₹';
    
    // Tax
    const TAX_RATE = 18; // 18% GST
    
    // Order Status
    const ORDER_STATUS = [
        'pending' => 'Pending',
        'confirmed' => 'Confirmed',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'refunded' => 'Refunded'
    ];
    
    // Payment Status
    const PAYMENT_STATUS = [
        'pending' => 'Pending',
        'paid' => 'Paid',
        'failed' => 'Failed',
        'refunded' => 'Refunded'
    ];
}

/**
 * Utility Functions
 */
class Utils {
    public static function generateOrderNumber() {
        return 'WD' . date('Y') . date('m') . date('d') . rand(1000, 9999);
    }
    
    public static function formatCurrency($amount) {
        return Config::CURRENCY_SYMBOL . number_format($amount, 2);
    }
    
    public static function calculateTax($amount) {
        return ($amount * Config::TAX_RATE) / 100;
    }
    
    public static function sanitizeInput($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
    
    public static function generateSlug($string) {
        $slug = strtolower($string);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug;
    }
    
    public static function uploadFile($file, $directory = 'general') {
        $uploadDir = Config::UPLOAD_PATH . $directory . '/';
        
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        $fileName = time() . '_' . basename($file['name']);
        $targetPath = $uploadDir . $fileName;
        $fileExtension = strtolower(pathinfo($targetPath, PATHINFO_EXTENSION));
        
        // Check file size
        if ($file['size'] > Config::MAX_FILE_SIZE) {
            return ['success' => false, 'message' => 'File size too large'];
        }
        
        // Check file extension
        if (!in_array($fileExtension, Config::ALLOWED_EXTENSIONS)) {
            return ['success' => false, 'message' => 'File type not allowed'];
        }
        
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            return [
                'success' => true, 
                'filename' => $fileName,
                'path' => $targetPath
            ];
        } else {
            return ['success' => false, 'message' => 'Upload failed'];
        }
    }
}

/**
 * Session Management
 */
class Session {
    public static function start() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    public static function set($key, $value) {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    public static function get($key, $default = null) {
        self::start();
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    public static function remove($key) {
        self::start();
        unset($_SESSION[$key]);
    }
    
    public static function destroy() {
        self::start();
        session_destroy();
    }
    
    public static function isLoggedIn() {
        return self::get('user_id') !== null;
    }
    
    public static function isAdmin() {
        return self::get('user_role') === 'admin';
    }
}
?>
