<?php
/**
 * Razorpay Payment Integration
 * Website Developer 0002 E-commerce Platform
 */

require_once 'config/database.php';

class RazorpayPayment {
    private $keyId;
    private $keySecret;
    private $conn;
    
    public function __construct() {
        $this->keyId = Config::RAZORPAY_KEY_ID;
        $this->keySecret = Config::RAZORPAY_KEY_SECRET;
        
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    /**
     * Create Razorpay order
     */
    public function createOrder($amount, $currency = 'INR', $receipt = null, $notes = []) {
        $url = 'https://api.razorpay.com/v1/orders';
        
        $data = [
            'amount' => $amount * 100, // Amount in paise
            'currency' => $currency,
            'receipt' => $receipt ?: 'order_' . time(),
            'notes' => $notes
        ];
        
        $response = $this->makeApiCall($url, $data, 'POST');
        
        if ($response && isset($response['id'])) {
            return $response;
        }
        
        return false;
    }
    
    /**
     * Verify payment signature
     */
    public function verifyPaymentSignature($razorpayOrderId, $razorpayPaymentId, $razorpaySignature) {
        $body = $razorpayOrderId . "|" . $razorpayPaymentId;
        $expectedSignature = hash_hmac('sha256', $body, $this->keySecret);
        
        return hash_equals($expectedSignature, $razorpaySignature);
    }
    
    /**
     * Get payment details
     */
    public function getPaymentDetails($paymentId) {
        $url = "https://api.razorpay.com/v1/payments/{$paymentId}";
        return $this->makeApiCall($url, [], 'GET');
    }
    
    /**
     * Get order details
     */
    public function getOrderDetails($orderId) {
        $url = "https://api.razorpay.com/v1/orders/{$orderId}";
        return $this->makeApiCall($url, [], 'GET');
    }
    
    /**
     * Capture payment
     */
    public function capturePayment($paymentId, $amount) {
        $url = "https://api.razorpay.com/v1/payments/{$paymentId}/capture";
        $data = ['amount' => $amount * 100]; // Amount in paise
        
        return $this->makeApiCall($url, $data, 'POST');
    }
    
    /**
     * Refund payment
     */
    public function refundPayment($paymentId, $amount = null, $notes = []) {
        $url = "https://api.razorpay.com/v1/payments/{$paymentId}/refund";
        
        $data = ['notes' => $notes];
        if ($amount) {
            $data['amount'] = $amount * 100; // Amount in paise
        }
        
        return $this->makeApiCall($url, $data, 'POST');
    }
    
    /**
     * Create customer
     */
    public function createCustomer($name, $email, $contact = null, $notes = []) {
        $url = 'https://api.razorpay.com/v1/customers';
        
        $data = [
            'name' => $name,
            'email' => $email,
            'notes' => $notes
        ];
        
        if ($contact) {
            $data['contact'] = $contact;
        }
        
        return $this->makeApiCall($url, $data, 'POST');
    }
    
    /**
     * Process webhook
     */
    public function processWebhook($payload, $signature) {
        // Verify webhook signature
        $expectedSignature = hash_hmac('sha256', $payload, Config::RAZORPAY_WEBHOOK_SECRET);
        
        if (!hash_equals($expectedSignature, $signature)) {
            return ['success' => false, 'message' => 'Invalid signature'];
        }
        
        $data = json_decode($payload, true);
        
        if (!$data) {
            return ['success' => false, 'message' => 'Invalid payload'];
        }
        
        $event = $data['event'];
        $payment = $data['payload']['payment']['entity'] ?? null;
        $order = $data['payload']['order']['entity'] ?? null;
        
        switch ($event) {
            case 'payment.captured':
                return $this->handlePaymentCaptured($payment);
                
            case 'payment.failed':
                return $this->handlePaymentFailed($payment);
                
            case 'order.paid':
                return $this->handleOrderPaid($order);
                
            default:
                return ['success' => true, 'message' => 'Event not handled'];
        }
    }
    
    /**
     * Handle payment captured webhook
     */
    private function handlePaymentCaptured($payment) {
        if (!$payment) {
            return ['success' => false, 'message' => 'No payment data'];
        }
        
        $paymentId = $payment['id'];
        $orderId = $payment['order_id'];
        $amount = $payment['amount'] / 100; // Convert from paise
        
        // Update order payment status
        $query = "UPDATE orders SET 
                    payment_status = 'paid',
                    razorpay_payment_id = :payment_id,
                    status = 'confirmed'
                  WHERE razorpay_order_id = :order_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':payment_id', $paymentId);
        $stmt->bindParam(':order_id', $orderId);
        
        if ($stmt->execute()) {
            // Log transaction
            $this->logTransaction($orderId, $paymentId, $amount, 'success', $payment);
            
            return ['success' => true, 'message' => 'Payment updated'];
        }
        
        return ['success' => false, 'message' => 'Database update failed'];
    }
    
    /**
     * Handle payment failed webhook
     */
    private function handlePaymentFailed($payment) {
        if (!$payment) {
            return ['success' => false, 'message' => 'No payment data'];
        }
        
        $paymentId = $payment['id'];
        $orderId = $payment['order_id'];
        $amount = $payment['amount'] / 100;
        
        // Update order payment status
        $query = "UPDATE orders SET 
                    payment_status = 'failed',
                    razorpay_payment_id = :payment_id
                  WHERE razorpay_order_id = :order_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':payment_id', $paymentId);
        $stmt->bindParam(':order_id', $orderId);
        
        if ($stmt->execute()) {
            // Log transaction
            $this->logTransaction($orderId, $paymentId, $amount, 'failed', $payment);
            
            return ['success' => true, 'message' => 'Payment failure updated'];
        }
        
        return ['success' => false, 'message' => 'Database update failed'];
    }
    
    /**
     * Handle order paid webhook
     */
    private function handleOrderPaid($order) {
        // Similar to payment captured
        return $this->handlePaymentCaptured($order);
    }
    
    /**
     * Log transaction
     */
    private function logTransaction($orderId, $paymentId, $amount, $status, $gatewayResponse) {
        $query = "INSERT INTO payment_transactions 
                  (order_id, transaction_id, payment_gateway, amount, status, gateway_response)
                  VALUES (
                    (SELECT id FROM orders WHERE razorpay_order_id = :order_id),
                    :transaction_id, 'razorpay', :amount, :status, :gateway_response
                  )";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':order_id', $orderId);
        $stmt->bindParam(':transaction_id', $paymentId);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':gateway_response', json_encode($gatewayResponse));
        
        return $stmt->execute();
    }
    
    /**
     * Make API call to Razorpay
     */
    private function makeApiCall($url, $data = [], $method = 'GET') {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => $this->keyId . ':' . $this->keySecret,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json'
            ]
        ]);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return json_decode($response, true);
        }
        
        // Log error
        error_log("Razorpay API Error: HTTP $httpCode - $response");
        return false;
    }
    
    /**
     * Generate payment form HTML
     */
    public function generatePaymentForm($orderId, $amount, $currency, $customerDetails, $orderDetails) {
        $html = '
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
        <script>
        function initiatePayment() {
            var options = {
                "key": "' . $this->keyId . '",
                "amount": ' . ($amount * 100) . ',
                "currency": "' . $currency . '",
                "name": "' . Config::SITE_NAME . '",
                "description": "' . htmlspecialchars($orderDetails['description'] ?? 'Service Payment') . '",
                "order_id": "' . $orderId . '",
                "handler": function (response) {
                    // Handle successful payment
                    handlePaymentSuccess(response);
                },
                "prefill": {
                    "name": "' . htmlspecialchars($customerDetails['name']) . '",
                    "email": "' . htmlspecialchars($customerDetails['email']) . '",
                    "contact": "' . htmlspecialchars($customerDetails['phone'] ?? '') . '"
                },
                "theme": {
                    "color": "#667eea"
                },
                "modal": {
                    "ondismiss": function() {
                        // Handle payment cancellation
                        handlePaymentCancel();
                    }
                }
            };
            
            var rzp = new Razorpay(options);
            rzp.open();
        }
        
        function handlePaymentSuccess(response) {
            // Send payment details to server for verification
            fetch("payment-verify.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_signature: response.razorpay_signature
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = "payment-success.php?order=" + response.razorpay_order_id;
                } else {
                    alert("Payment verification failed. Please contact support.");
                }
            })
            .catch(error => {
                console.error("Error:", error);
                alert("An error occurred. Please try again.");
            });
        }
        
        function handlePaymentCancel() {
            console.log("Payment cancelled by user");
        }
        </script>';
        
        return $html;
    }
}
?>
