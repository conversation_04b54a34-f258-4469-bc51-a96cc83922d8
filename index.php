<?php
session_start();
require_once 'config/database.php';
require_once 'models/User.php';
require_once 'models/ServicePackage.php';
require_once 'models/Order.php';

// Get current page
$page = isset($_GET['page']) ? $_GET['page'] : 'home';
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Initialize models
$servicePackage = new ServicePackage();
$user = new User();
$order = new Order();

// Handle AJAX requests
if (isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'login':
            $email = Utils::sanitizeInput($_POST['email']);
            $password = $_POST['password'];
            
            if ($user->login($email, $password)) {
                echo json_encode(['success' => true, 'message' => 'Login successful']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
            }
            exit;
            
        case 'register':
            $user->username = Utils::sanitizeInput($_POST['username']);
            $user->email = Utils::sanitizeInput($_POST['email']);
            $user->password = $_POST['password'];
            $user->full_name = Utils::sanitizeInput($_POST['full_name']);
            $user->phone = Utils::sanitizeInput($_POST['phone']);
            $user->role = 'client';
            
            // Validation
            if ($user->emailExists($user->email)) {
                echo json_encode(['success' => false, 'message' => 'Email already exists']);
                exit;
            }
            
            if ($user->usernameExists($user->username)) {
                echo json_encode(['success' => false, 'message' => 'Username already exists']);
                exit;
            }
            
            if ($user->register()) {
                echo json_encode(['success' => true, 'message' => 'Registration successful']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Registration failed']);
            }
            exit;
            
        case 'get_package':
            $package_id = $_POST['package_id'];
            $package = $servicePackage->getById($package_id);
            
            if ($package) {
                echo json_encode(['success' => true, 'package' => $package]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Package not found']);
            }
            exit;
    }
}

// Handle logout
if ($action === 'logout') {
    User::logout();
    header('Location: index.php');
    exit;
}

// Get featured packages for homepage
$featuredPackages = $servicePackage->getFeatured(6);

// Get all categories
$database = new Database();
$conn = $database->getConnection();
$categoryQuery = "SELECT * FROM service_categories WHERE status = 'active' ORDER BY name";
$categoryStmt = $conn->prepare($categoryQuery);
$categoryStmt->execute();
$categories = $categoryStmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page === 'home' ? 'Website Developer 0002 - Professional Web Development Services' : ucfirst($page) . ' - Website Developer 0002'; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Professional web development services including website design, e-commerce solutions, mobile apps, and digital marketing. Get your project done by experts.">
    <meta name="keywords" content="web development, website design, e-commerce, mobile apps, digital marketing, PHP, WordPress">
    <meta name="author" content="Website Developer 0002">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Website Developer 0002 - Professional Web Development Services">
    <meta property="og:description" content="Get professional web development services with guaranteed delivery and unlimited revisions.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo Config::SITE_URL; ?>">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i>Website Developer 0002
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'home' ? 'active' : ''; ?>" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'services' ? 'active' : ''; ?>" href="?page=services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'portfolio' ? 'active' : ''; ?>" href="?page=portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'contact' ? 'active' : ''; ?>" href="?page=contact">Contact</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (Session::isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i><?php echo Session::get('user_name'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (Session::isAdmin()): ?>
                                    <li><a class="dropdown-item" href="?page=admin"><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="?page=dashboard"><i class="fas fa-tachometer-alt me-2"></i>My Dashboard</a></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="?page=profile"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="?action=logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <button class="btn btn-outline-light me-2" data-bs-toggle="modal" data-bs-target="#loginModal">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#registerModal">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </button>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <?php
        switch ($page) {
            case 'home':
                include 'pages/home.php';
                break;
            case 'services':
                include 'pages/services.php';
                break;
            case 'service':
                include 'pages/service-detail.php';
                break;
            case 'portfolio':
                include 'pages/portfolio.php';
                break;
            case 'contact':
                include 'pages/contact.php';
                break;
            case 'dashboard':
                if (Session::isLoggedIn()) {
                    include 'pages/dashboard.php';
                } else {
                    header('Location: index.php');
                    exit;
                }
                break;
            case 'admin':
                if (Session::isAdmin()) {
                    include 'pages/admin/dashboard.php';
                } else {
                    header('Location: index.php');
                    exit;
                }
                break;
            case 'checkout':
                if (Session::isLoggedIn()) {
                    include 'pages/checkout.php';
                } else {
                    header('Location: index.php');
                    exit;
                }
                break;
            default:
                include 'pages/404.php';
        }
        ?>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-code me-2"></i>Website Developer 0002</h5>
                    <p>Professional web development services with guaranteed quality and timely delivery.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="?page=services" class="text-light">Our Services</a></li>
                        <li><a href="?page=portfolio" class="text-light">Portfolio</a></li>
                        <li><a href="?page=contact" class="text-light">Contact Us</a></li>
                        <li><a href="#" class="text-light">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i>+91-9876543210</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i>India</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> Website Developer 0002. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <?php include 'includes/modals/login.php'; ?>
    
    <!-- Register Modal -->
    <?php include 'includes/modals/register.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
