<?php
require_once 'config/database.php';

class Order {
    private $conn;
    private $table = 'orders';
    
    public $id;
    public $order_number;
    public $user_id;
    public $package_id;
    public $package_name;
    public $package_price;
    public $custom_requirements;
    public $status;
    public $payment_status;
    public $payment_method;
    public $payment_id;
    public $razorpay_order_id;
    public $razorpay_payment_id;
    public $razorpay_signature;
    public $total_amount;
    public $delivery_date;
    public $notes;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    // Create new order
    public function create() {
        $query = "INSERT INTO " . $this->table . " 
                  (order_number, user_id, package_id, package_name, package_price,
                   custom_requirements, status, payment_status, total_amount, delivery_date) 
                  VALUES (:order_number, :user_id, :package_id, :package_name, :package_price,
                          :custom_requirements, :status, :payment_status, :total_amount, :delivery_date)";
        
        $stmt = $this->conn->prepare($query);
        
        // Generate order number if not provided
        if (empty($this->order_number)) {
            $this->order_number = Utils::generateOrderNumber();
        }
        
        // Calculate delivery date
        if (empty($this->delivery_date)) {
            $this->delivery_date = date('Y-m-d', strtotime('+7 days'));
        }
        
        $stmt->bindParam(':order_number', $this->order_number);
        $stmt->bindParam(':user_id', $this->user_id);
        $stmt->bindParam(':package_id', $this->package_id);
        $stmt->bindParam(':package_name', $this->package_name);
        $stmt->bindParam(':package_price', $this->package_price);
        $stmt->bindParam(':custom_requirements', $this->custom_requirements);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':payment_status', $this->payment_status);
        $stmt->bindParam(':total_amount', $this->total_amount);
        $stmt->bindParam(':delivery_date', $this->delivery_date);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            
            // Create default milestones
            $this->createDefaultMilestones();
            
            return true;
        }
        return false;
    }
    
    // Create default project milestones
    private function createDefaultMilestones() {
        $milestones = [
            'Requirements Analysis',
            'Design & Planning',
            'Development',
            'Testing & Review',
            'Delivery & Launch'
        ];
        
        $query = "INSERT INTO order_progress (order_id, milestone, status) VALUES (:order_id, :milestone, 'pending')";
        $stmt = $this->conn->prepare($query);
        
        foreach ($milestones as $milestone) {
            $stmt->bindParam(':order_id', $this->id);
            $stmt->bindParam(':milestone', $milestone);
            $stmt->execute();
        }
    }
    
    // Get order by ID
    public function getById($id) {
        $query = "SELECT o.*, u.full_name as client_name, u.email as client_email, u.phone as client_phone,
                         sp.name as service_name, sp.delivery_days
                  FROM " . $this->table . " o
                  LEFT JOIN users u ON o.user_id = u.id
                  LEFT JOIN service_packages sp ON o.package_id = sp.id
                  WHERE o.id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->rowCount() > 0 ? $stmt->fetch() : false;
    }
    
    // Get order by order number
    public function getByOrderNumber($order_number) {
        $query = "SELECT o.*, u.full_name as client_name, u.email as client_email
                  FROM " . $this->table . " o
                  LEFT JOIN users u ON o.user_id = u.id
                  WHERE o.order_number = :order_number";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':order_number', $order_number);
        $stmt->execute();
        
        return $stmt->rowCount() > 0 ? $stmt->fetch() : false;
    }
    
    // Get user orders
    public function getUserOrders($user_id, $limit = 10, $offset = 0) {
        $query = "SELECT o.*, sp.name as service_name
                  FROM " . $this->table . " o
                  LEFT JOIN service_packages sp ON o.package_id = sp.id
                  WHERE o.user_id = :user_id
                  ORDER BY o.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    // Get all orders (admin)
    public function getAllOrders($limit = 20, $offset = 0, $status = null) {
        $whereClause = "";
        if ($status) {
            $whereClause = "WHERE o.status = :status";
        }
        
        $query = "SELECT o.*, u.full_name as client_name, u.email as client_email,
                         sp.name as service_name
                  FROM " . $this->table . " o
                  LEFT JOIN users u ON o.user_id = u.id
                  LEFT JOIN service_packages sp ON o.package_id = sp.id
                  " . $whereClause . "
                  ORDER BY o.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    // Update order status
    public function updateStatus($order_id, $status, $notes = null) {
        $query = "UPDATE " . $this->table . " SET status = :status";
        if ($notes) {
            $query .= ", notes = :notes";
        }
        $query .= " WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $order_id);
        
        if ($notes) {
            $stmt->bindParam(':notes', $notes);
        }
        
        return $stmt->execute();
    }
    
    // Update payment status
    public function updatePaymentStatus($order_id, $payment_status, $payment_data = []) {
        $query = "UPDATE " . $this->table . " 
                  SET payment_status = :payment_status";
        
        $params = [':payment_status' => $payment_status, ':id' => $order_id];
        
        if (isset($payment_data['payment_method'])) {
            $query .= ", payment_method = :payment_method";
            $params[':payment_method'] = $payment_data['payment_method'];
        }
        
        if (isset($payment_data['payment_id'])) {
            $query .= ", payment_id = :payment_id";
            $params[':payment_id'] = $payment_data['payment_id'];
        }
        
        if (isset($payment_data['razorpay_order_id'])) {
            $query .= ", razorpay_order_id = :razorpay_order_id";
            $params[':razorpay_order_id'] = $payment_data['razorpay_order_id'];
        }
        
        if (isset($payment_data['razorpay_payment_id'])) {
            $query .= ", razorpay_payment_id = :razorpay_payment_id";
            $params[':razorpay_payment_id'] = $payment_data['razorpay_payment_id'];
        }
        
        if (isset($payment_data['razorpay_signature'])) {
            $query .= ", razorpay_signature = :razorpay_signature";
            $params[':razorpay_signature'] = $payment_data['razorpay_signature'];
        }
        
        $query .= " WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $param => $value) {
            $stmt->bindValue($param, $value);
        }
        
        return $stmt->execute();
    }
    
    // Get order progress
    public function getOrderProgress($order_id) {
        $query = "SELECT * FROM order_progress WHERE order_id = :order_id ORDER BY id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    // Update milestone status
    public function updateMilestone($milestone_id, $status, $description = null) {
        $query = "UPDATE order_progress SET status = :status";
        if ($description) {
            $query .= ", description = :description";
        }
        if ($status === 'completed') {
            $query .= ", completion_date = NOW()";
        }
        $query .= " WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $milestone_id);
        
        if ($description) {
            $stmt->bindParam(':description', $description);
        }
        
        return $stmt->execute();
    }
    
    // Get order statistics
    public function getOrderStats() {
        $query = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as active_orders,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                    SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
                    AVG(total_amount) as average_order_value
                  FROM " . $this->table;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    // Get monthly revenue
    public function getMonthlyRevenue($year = null) {
        if (!$year) {
            $year = date('Y');
        }
        
        $query = "SELECT 
                    MONTH(created_at) as month,
                    SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as revenue,
                    COUNT(*) as orders
                  FROM " . $this->table . "
                  WHERE YEAR(created_at) = :year
                  GROUP BY MONTH(created_at)
                  ORDER BY month";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':year', $year);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
}
?>
