<?php
require_once 'config/database.php';

class ServicePackage {
    private $conn;
    private $table = 'service_packages';
    
    public $id;
    public $category_id;
    public $name;
    public $slug;
    public $short_description;
    public $full_description;
    public $features;
    public $price;
    public $discount_price;
    public $delivery_days;
    public $revisions;
    public $image;
    public $gallery;
    public $status;
    public $featured;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    // Create new service package
    public function create() {
        $query = "INSERT INTO " . $this->table . " 
                  (category_id, name, slug, short_description, full_description, 
                   features, price, discount_price, delivery_days, revisions, 
                   image, gallery, status, featured) 
                  VALUES (:category_id, :name, :slug, :short_description, :full_description,
                          :features, :price, :discount_price, :delivery_days, :revisions,
                          :image, :gallery, :status, :featured)";
        
        $stmt = $this->conn->prepare($query);
        
        // Generate slug if not provided
        if (empty($this->slug)) {
            $this->slug = Utils::generateSlug($this->name);
        }
        
        // Convert arrays to JSON
        $features_json = is_array($this->features) ? json_encode($this->features) : $this->features;
        $gallery_json = is_array($this->gallery) ? json_encode($this->gallery) : $this->gallery;
        
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':slug', $this->slug);
        $stmt->bindParam(':short_description', $this->short_description);
        $stmt->bindParam(':full_description', $this->full_description);
        $stmt->bindParam(':features', $features_json);
        $stmt->bindParam(':price', $this->price);
        $stmt->bindParam(':discount_price', $this->discount_price);
        $stmt->bindParam(':delivery_days', $this->delivery_days);
        $stmt->bindParam(':revisions', $this->revisions);
        $stmt->bindParam(':image', $this->image);
        $stmt->bindParam(':gallery', $gallery_json);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':featured', $this->featured);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }
    
    // Get all active packages
    public function getAllActive($limit = 12, $offset = 0, $category_id = null) {
        $whereClause = "WHERE sp.status = 'active'";
        if ($category_id) {
            $whereClause .= " AND sp.category_id = :category_id";
        }
        
        $query = "SELECT sp.*, sc.name as category_name 
                  FROM " . $this->table . " sp
                  LEFT JOIN service_categories sc ON sp.category_id = sc.id
                  " . $whereClause . "
                  ORDER BY sp.featured DESC, sp.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        
        if ($category_id) {
            $stmt->bindParam(':category_id', $category_id);
        }
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $packages = $stmt->fetchAll();
        
        // Decode JSON fields
        foreach ($packages as &$package) {
            $package['features'] = json_decode($package['features'], true);
            $package['gallery'] = json_decode($package['gallery'], true);
        }
        
        return $packages;
    }
    
    // Get featured packages
    public function getFeatured($limit = 6) {
        $query = "SELECT sp.*, sc.name as category_name 
                  FROM " . $this->table . " sp
                  LEFT JOIN service_categories sc ON sp.category_id = sc.id
                  WHERE sp.status = 'active' AND sp.featured = 1
                  ORDER BY sp.created_at DESC
                  LIMIT :limit";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $packages = $stmt->fetchAll();
        
        // Decode JSON fields
        foreach ($packages as &$package) {
            $package['features'] = json_decode($package['features'], true);
            $package['gallery'] = json_decode($package['gallery'], true);
        }
        
        return $packages;
    }
    
    // Get package by ID
    public function getById($id) {
        $query = "SELECT sp.*, sc.name as category_name 
                  FROM " . $this->table . " sp
                  LEFT JOIN service_categories sc ON sp.category_id = sc.id
                  WHERE sp.id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $package = $stmt->fetch();
            $package['features'] = json_decode($package['features'], true);
            $package['gallery'] = json_decode($package['gallery'], true);
            return $package;
        }
        return false;
    }
    
    // Get package by slug
    public function getBySlug($slug) {
        $query = "SELECT sp.*, sc.name as category_name 
                  FROM " . $this->table . " sp
                  LEFT JOIN service_categories sc ON sp.category_id = sc.id
                  WHERE sp.slug = :slug AND sp.status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $package = $stmt->fetch();
            $package['features'] = json_decode($package['features'], true);
            $package['gallery'] = json_decode($package['gallery'], true);
            return $package;
        }
        return false;
    }
    
    // Update package
    public function update() {
        $query = "UPDATE " . $this->table . " 
                  SET category_id = :category_id, name = :name, slug = :slug,
                      short_description = :short_description, full_description = :full_description,
                      features = :features, price = :price, discount_price = :discount_price,
                      delivery_days = :delivery_days, revisions = :revisions,
                      image = :image, gallery = :gallery, status = :status, featured = :featured
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Convert arrays to JSON
        $features_json = is_array($this->features) ? json_encode($this->features) : $this->features;
        $gallery_json = is_array($this->gallery) ? json_encode($this->gallery) : $this->gallery;
        
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':slug', $this->slug);
        $stmt->bindParam(':short_description', $this->short_description);
        $stmt->bindParam(':full_description', $this->full_description);
        $stmt->bindParam(':features', $features_json);
        $stmt->bindParam(':price', $this->price);
        $stmt->bindParam(':discount_price', $this->discount_price);
        $stmt->bindParam(':delivery_days', $this->delivery_days);
        $stmt->bindParam(':revisions', $this->revisions);
        $stmt->bindParam(':image', $this->image);
        $stmt->bindParam(':gallery', $gallery_json);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':featured', $this->featured);
        $stmt->bindParam(':id', $this->id);
        
        return $stmt->execute();
    }
    
    // Delete package
    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
    
    // Search packages
    public function search($keyword, $limit = 12, $offset = 0) {
        $query = "SELECT sp.*, sc.name as category_name 
                  FROM " . $this->table . " sp
                  LEFT JOIN service_categories sc ON sp.category_id = sc.id
                  WHERE sp.status = 'active' AND 
                        (sp.name LIKE :keyword OR sp.short_description LIKE :keyword)
                  ORDER BY sp.featured DESC, sp.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $searchTerm = '%' . $keyword . '%';
        $stmt->bindParam(':keyword', $searchTerm);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $packages = $stmt->fetchAll();
        
        // Decode JSON fields
        foreach ($packages as &$package) {
            $package['features'] = json_decode($package['features'], true);
            $package['gallery'] = json_decode($package['gallery'], true);
        }
        
        return $packages;
    }
    
    // Get package statistics
    public function getStats() {
        $query = "SELECT 
                    COUNT(*) as total_packages,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_packages,
                    SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured_packages,
                    AVG(price) as average_price
                  FROM " . $this->table;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch();
    }
}
?>
