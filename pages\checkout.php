<?php
// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Get package ID from URL
$package_id = isset($_GET['package']) ? (int)$_GET['package'] : 0;

if (!$package_id) {
    header('Location: ?page=services');
    exit;
}

// Get package details
$package = $servicePackage->getById($package_id);

if (!$package) {
    header('Location: ?page=services');
    exit;
}

// Get user details
$user_id = Session::get('user_id');
$userDetails = $user->getUserById($user_id);

// Calculate pricing
$basePrice = $package['discount_price'] ?: $package['price'];
$taxAmount = Utils::calculateTax($basePrice);
$totalAmount = $basePrice + $taxAmount;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['place_order'])) {
    require_once 'includes/razorpay/RazorpayPayment.php';
    
    // Create order
    $order = new Order();
    $order->user_id = $user_id;
    $order->package_id = $package_id;
    $order->package_name = $package['name'];
    $order->package_price = $basePrice;
    $order->custom_requirements = Utils::sanitizeInput($_POST['requirements']);
    $order->status = 'pending';
    $order->payment_status = 'pending';
    $order->total_amount = $totalAmount;
    
    // Calculate delivery date
    $deliveryDate = date('Y-m-d', strtotime('+' . $package['delivery_days'] . ' days'));
    $order->delivery_date = $deliveryDate;
    
    if ($order->create()) {
        // Create Razorpay order
        $razorpay = new RazorpayPayment();
        $razorpayOrder = $razorpay->createOrder(
            $totalAmount,
            'INR',
            $order->order_number,
            [
                'order_id' => $order->id,
                'package_name' => $package['name'],
                'customer_email' => $userDetails['email']
            ]
        );
        
        if ($razorpayOrder) {
            // Update order with Razorpay order ID
            $updateQuery = "UPDATE orders SET razorpay_order_id = :razorpay_order_id WHERE id = :id";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bindParam(':razorpay_order_id', $razorpayOrder['id']);
            $updateStmt->bindParam(':id', $order->id);
            $updateStmt->execute();
            
            // Redirect to payment page
            header('Location: payment.php?order=' . $order->order_number);
            exit;
        } else {
            $error = "Failed to create payment order. Please try again.";
        }
    } else {
        $error = "Failed to create order. Please try again.";
    }
}
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <!-- Order Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Order Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <?php if ($package['image']): ?>
                                <img src="<?php echo $package['image']; ?>" class="img-fluid rounded" 
                                     alt="<?php echo htmlspecialchars($package['name']); ?>">
                            <?php else: ?>
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8">
                            <h4><?php echo htmlspecialchars($package['name']); ?></h4>
                            <p class="text-muted"><?php echo htmlspecialchars($package['short_description']); ?></p>
                            
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Category:</strong><br>
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($package['category_name']); ?></span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Delivery Time:</strong><br>
                                    <i class="fas fa-clock text-warning me-1"></i><?php echo $package['delivery_days']; ?> days
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Revisions:</strong><br>
                                    <i class="fas fa-redo text-info me-1"></i><?php echo $package['revisions']; ?> revisions
                                </div>
                                <div class="col-sm-6">
                                    <strong>Price:</strong><br>
                                    <?php if ($package['discount_price']): ?>
                                        <span class="text-muted text-decoration-line-through">
                                            <?php echo Utils::formatCurrency($package['price']); ?>
                                        </span><br>
                                        <span class="h5 text-success">
                                            <?php echo Utils::formatCurrency($package['discount_price']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="h5 text-primary">
                                            <?php echo Utils::formatCurrency($package['price']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Features -->
                    <?php if (!empty($package['features'])): ?>
                        <hr>
                        <h6>What's Included:</h6>
                        <div class="row">
                            <?php foreach ($package['features'] as $feature): ?>
                                <div class="col-md-6 mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <?php echo htmlspecialchars($feature); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Order Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Project Requirements</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="requirements" class="form-label">
                                Project Requirements <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="requirements" name="requirements" rows="6" 
                                      placeholder="Please describe your project requirements in detail..." required></textarea>
                            <div class="form-text">
                                Provide as much detail as possible to help us deliver exactly what you need.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                    <a href="#" target="_blank">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="place_order" class="btn btn-primary btn-lg">
                                <i class="fas fa-credit-card me-2"></i>Proceed to Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="card sticky-top" style="top: 100px;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Service Price:</span>
                        <span><?php echo Utils::formatCurrency($basePrice); ?></span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax (<?php echo Config::TAX_RATE; ?>%):</span>
                        <span><?php echo Utils::formatCurrency($taxAmount); ?></span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total Amount:</strong>
                        <strong class="text-primary"><?php echo Utils::formatCurrency($totalAmount); ?></strong>
                    </div>
                    
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            Secure payment powered by Razorpay. We accept all major credit/debit cards, 
                            UPI, and net banking.
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Customer Details -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Customer Details</h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong><?php echo htmlspecialchars($userDetails['full_name']); ?></strong></p>
                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($userDetails['email']); ?></p>
                    <?php if ($userDetails['phone']): ?>
                        <p class="mb-0 text-muted"><?php echo htmlspecialchars($userDetails['phone']); ?></p>
                    <?php endif; ?>
                    
                    <hr>
                    
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Your information is secure and will only be used for order processing.
                    </small>
                </div>
            </div>
            
            <!-- Support -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h6><i class="fas fa-headset me-2"></i>Need Help?</h6>
                    <p class="small text-muted mb-3">
                        Our support team is here to help you with any questions.
                    </p>
                    <a href="?page=contact" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-phone me-1"></i>Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sticky-top {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

@media (max-width: 991px) {
    .sticky-top {
        position: static;
    }
}
</style>
