<?php
// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$user_id = Session::get('user_id');

// Get user orders
$userOrders = $order->getUserOrders($user_id);

// Get user statistics
$userStats = $order->getUserStats($user_id);
?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">My Dashboard</h1>
            <p class="text-muted">Welcome back, <?php echo Session::get('user_name'); ?>!</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?php echo $userStats['total_orders'] ?? 0; ?></div>
                            <div class="small">Total Orders</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?php echo $userStats['completed_orders'] ?? 0; ?></div>
                            <div class="small">Completed</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?php echo $userStats['active_orders'] ?? 0; ?></div>
                            <div class="small">In Progress</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?php echo Utils::formatCurrency($userStats['total_spent'] ?? 0); ?></div>
                            <div class="small">Total Spent</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-rupee-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="?page=services" class="btn btn-primary btn-block w-100">
                                <i class="fas fa-plus me-2"></i>New Order
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="?page=profile" class="btn btn-outline-secondary btn-block w-100">
                                <i class="fas fa-user me-2"></i>Edit Profile
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="?page=contact" class="btn btn-outline-info btn-block w-100">
                                <i class="fas fa-headset me-2"></i>Support
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="?page=portfolio" class="btn btn-outline-success btn-block w-100">
                                <i class="fas fa-eye me-2"></i>View Portfolio
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>My Orders</h5>
                    <a href="?page=orders" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    <?php if (!empty($userOrders)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Service</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Delivery Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($userOrders, 0, 5) as $orderItem): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($orderItem['order_number']); ?></strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($orderItem['service_image']): ?>
                                                        <img src="<?php echo $orderItem['service_image']; ?>" 
                                                             class="rounded me-2" width="40" height="40" 
                                                             style="object-fit: cover;">
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($orderItem['service_name']); ?></div>
                                                        <small class="text-muted"><?php echo htmlspecialchars($orderItem['category_name']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?php echo Utils::formatCurrency($orderItem['total_amount']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getStatusColor($orderItem['status']); ?>">
                                                    <?php echo ucfirst(str_replace('_', ' ', $orderItem['status'])); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getPaymentStatusColor($orderItem['payment_status']); ?>">
                                                    <?php echo ucfirst($orderItem['payment_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y', strtotime($orderItem['delivery_date'])); ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?page=order&id=<?php echo $orderItem['id']; ?>" 
                                                       class="btn btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($orderItem['payment_status'] === 'paid'): ?>
                                                        <a href="invoice.php?order=<?php echo $orderItem['order_number']; ?>" 
                                                           class="btn btn-outline-success" title="Download Invoice" target="_blank">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (in_array($orderItem['status'], ['pending', 'confirmed'])): ?>
                                                        <button class="btn btn-outline-danger" title="Cancel Order"
                                                                onclick="cancelOrder(<?php echo $orderItem['id']; ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No Orders Yet</h5>
                            <p class="text-muted">You haven't placed any orders yet. Browse our services to get started!</p>
                            <a href="?page=services" class="btn btn-primary">
                                <i class="fas fa-shopping-cart me-2"></i>Browse Services
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <?php if (!empty($userOrders)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <?php foreach (array_slice($userOrders, 0, 3) as $orderItem): ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-<?php echo getStatusColor($orderItem['status']); ?>"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Order #<?php echo $orderItem['order_number']; ?></h6>
                                        <p class="mb-1"><?php echo htmlspecialchars($orderItem['service_name']); ?></p>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y g:i A', strtotime($orderItem['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this order?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone. If payment was made, refund will be processed within 5-7 business days.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Order</button>
                <button type="button" class="btn btn-danger" id="confirmCancelBtn">Cancel Order</button>
            </div>
        </div>
    </div>
</div>

<script>
let orderToCancel = null;

function cancelOrder(orderId) {
    orderToCancel = orderId;
    const modal = new bootstrap.Modal(document.getElementById('cancelOrderModal'));
    modal.show();
}

document.getElementById('confirmCancelBtn').addEventListener('click', function() {
    if (orderToCancel) {
        // Send AJAX request to cancel order
        fetch('index.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `ajax=1&action=cancel_order&order_id=${orderToCancel}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to cancel order: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('cancelOrderModal')).hide();
    }
});
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.btn-block {
    width: 100%;
}
</style>

<?php
function getStatusColor($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'confirmed': return 'info';
        case 'in_progress': return 'primary';
        case 'completed': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

function getPaymentStatusColor($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'paid': return 'success';
        case 'failed': return 'danger';
        case 'refunded': return 'info';
        default: return 'secondary';
    }
}
?>
