<?php
// Get filter parameters
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
$search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
$page_num = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$limit = 12;
$offset = ($page_num - 1) * $limit;

// Get services based on filters
if ($search) {
    $services = $servicePackage->search($search, $limit, $offset);
} else {
    $services = $servicePackage->getAllActive($limit, $offset, $category_id);
}

// Get total count for pagination
$totalQuery = "SELECT COUNT(*) as total FROM service_packages sp WHERE sp.status = 'active'";
if ($category_id) {
    $totalQuery .= " AND sp.category_id = " . $category_id;
}
if ($search) {
    $totalQuery .= " AND (sp.name LIKE '%$search%' OR sp.short_description LIKE '%$search%')";
}

$totalStmt = $conn->prepare($totalQuery);
$totalStmt->execute();
$totalResult = $totalStmt->fetch();
$totalServices = $totalResult['total'];
$totalPages = ceil($totalServices / $limit);
?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold mb-3">Our Services</h1>
            <p class="lead text-muted">Professional web development solutions tailored to your needs</p>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <!-- Category Filters -->
            <div class="d-flex flex-wrap gap-2 mb-3">
                <a href="?page=services" class="btn <?php echo !$category_id ? 'btn-primary' : 'btn-outline-primary'; ?>">
                    All Services
                </a>
                <?php foreach ($categories as $category): ?>
                    <a href="?page=services&category=<?php echo $category['id']; ?>" 
                       class="btn <?php echo $category_id == $category['id'] ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        <i class="<?php echo $category['icon']; ?> me-1"></i>
                        <?php echo htmlspecialchars($category['name']); ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Search -->
            <form method="GET" class="d-flex">
                <input type="hidden" name="page" value="services">
                <?php if ($category_id): ?>
                    <input type="hidden" name="category" value="<?php echo $category_id; ?>">
                <?php endif; ?>
                <input type="text" class="form-control me-2" name="search" 
                       placeholder="Search services..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    
    <!-- Results Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <p class="text-muted mb-0">
                    Showing <?php echo count($services); ?> of <?php echo $totalServices; ?> services
                    <?php if ($search): ?>
                        for "<?php echo htmlspecialchars($search); ?>"
                    <?php endif; ?>
                </p>
                
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-sort me-1"></i>Sort by
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Price: Low to High</a></li>
                        <li><a class="dropdown-item" href="#">Price: High to Low</a></li>
                        <li><a class="dropdown-item" href="#">Delivery Time</a></li>
                        <li><a class="dropdown-item" href="#">Popularity</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Services Grid -->
    <div class="row">
        <?php if (!empty($services)): ?>
            <?php foreach ($services as $service): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card h-100">
                        <div class="card h-100 shadow-sm">
                            <?php if ($service['image']): ?>
                                <img src="<?php echo $service['image']; ?>" class="card-img-top" 
                                     alt="<?php echo htmlspecialchars($service['name']); ?>" 
                                     style="height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                     style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($service['category_name']); ?></span>
                                    <?php if ($service['featured']): ?>
                                        <span class="badge bg-warning">Featured</span>
                                    <?php endif; ?>
                                </div>
                                
                                <h5 class="card-title"><?php echo htmlspecialchars($service['name']); ?></h5>
                                <p class="card-text text-muted"><?php echo htmlspecialchars($service['short_description']); ?></p>
                                
                                <!-- Features -->
                                <?php if (!empty($service['features'])): ?>
                                    <ul class="list-unstyled mb-3">
                                        <?php foreach (array_slice($service['features'], 0, 3) as $feature): ?>
                                            <li class="small">
                                                <i class="fas fa-check text-success me-2"></i>
                                                <?php echo htmlspecialchars($feature); ?>
                                            </li>
                                        <?php endforeach; ?>
                                        <?php if (count($service['features']) > 3): ?>
                                            <li class="small text-muted">
                                                <i class="fas fa-plus me-2"></i>
                                                +<?php echo count($service['features']) - 3; ?> more features
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div class="price">
                                            <?php if ($service['discount_price']): ?>
                                                <span class="text-muted text-decoration-line-through small">
                                                    <?php echo Utils::formatCurrency($service['price']); ?>
                                                </span><br>
                                                <span class="h5 text-primary fw-bold">
                                                    <?php echo Utils::formatCurrency($service['discount_price']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="h5 text-primary fw-bold">
                                                    <?php echo Utils::formatCurrency($service['price']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted d-block">
                                                <i class="fas fa-clock me-1"></i><?php echo $service['delivery_days']; ?> days
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-redo me-1"></i><?php echo $service['revisions']; ?> revisions
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <a href="?page=service&slug=<?php echo $service['slug']; ?>" 
                                           class="btn btn-outline-primary">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a>
                                        <button class="btn btn-primary order-now-btn" 
                                                data-package-id="<?php echo $service['id']; ?>">
                                            <i class="fas fa-shopping-cart me-2"></i>Order Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">No services found</h3>
                    <p class="text-muted">Try adjusting your search criteria or browse all services.</p>
                    <a href="?page=services" class="btn btn-primary">
                        <i class="fas fa-th-large me-2"></i>View All Services
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="row mt-5">
            <div class="col-12">
                <nav aria-label="Services pagination">
                    <ul class="pagination justify-content-center">
                        <?php if ($page_num > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=services&p=<?php echo $page_num - 1; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page_num ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=services&p=<?php echo $i; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page_num < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=services&p=<?php echo $page_num + 1; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Quick Order Modal -->
<div class="modal fade" id="quickOrderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quick Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="quickOrderContent">
                    <!-- Content will be loaded via AJAX -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Set user login status for JavaScript
const userLoggedIn = <?php echo Session::isLoggedIn() ? 'true' : 'false'; ?>;
</script>
