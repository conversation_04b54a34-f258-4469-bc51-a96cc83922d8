<?php
session_start();
require_once 'config/database.php';
require_once 'models/Order.php';

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Get order number from URL
$order_number = isset($_GET['order']) ? Utils::sanitizeInput($_GET['order']) : '';

if (!$order_number) {
    header('Location: index.php');
    exit;
}

// Get order details
$order = new Order();
$orderDetails = $order->getByOrderNumber($order_number);

if (!$orderDetails || $orderDetails['user_id'] != Session::get('user_id')) {
    header('Location: index.php');
    exit;
}

// Check if payment is actually successful
if ($orderDetails['payment_status'] !== 'paid') {
    header('Location: payment.php?order=' . $order_number);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Website Developer 0002</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Success Header -->
                <div class="text-center mb-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle fa-5x text-success"></i>
                    </div>
                    <h1 class="h2 text-success mb-3">Payment Successful!</h1>
                    <p class="lead text-muted">
                        Thank you for your payment. Your order has been confirmed and we'll start working on it shortly.
                    </p>
                </div>
                
                <!-- Order Details Card -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Order Confirmation</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Order Details</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Order Number:</strong></td>
                                        <td><?php echo htmlspecialchars($orderDetails['order_number']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Service:</strong></td>
                                        <td><?php echo htmlspecialchars($orderDetails['package_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Amount Paid:</strong></td>
                                        <td class="text-success fw-bold">
                                            <?php echo Utils::formatCurrency($orderDetails['total_amount']); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Payment ID:</strong></td>
                                        <td><code><?php echo htmlspecialchars($orderDetails['razorpay_payment_id']); ?></code></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Timeline</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Order Date:</strong></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($orderDetails['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Payment Date:</strong></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($orderDetails['paid_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Expected Delivery:</strong></td>
                                        <td class="text-primary fw-bold">
                                            <?php echo date('M j, Y', strtotime($orderDetails['delivery_date'])); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-success">Confirmed</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Next Steps -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>What Happens Next?</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="text-center">
                                    <div class="step-icon mb-3">
                                        <i class="fas fa-user-tie fa-3x text-primary"></i>
                                    </div>
                                    <h6>1. Project Assignment</h6>
                                    <p class="small text-muted">
                                        Our team will review your requirements and assign a dedicated developer to your project.
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="text-center">
                                    <div class="step-icon mb-3">
                                        <i class="fas fa-code fa-3x text-info"></i>
                                    </div>
                                    <h6>2. Development Begins</h6>
                                    <p class="small text-muted">
                                        We'll start working on your project and keep you updated with regular progress reports.
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="text-center">
                                    <div class="step-icon mb-3">
                                        <i class="fas fa-rocket fa-3x text-success"></i>
                                    </div>
                                    <h6>3. Delivery & Support</h6>
                                    <p class="small text-muted">
                                        Your project will be delivered on time with ongoing support and revisions as needed.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-tachometer-alt fa-3x text-primary mb-3"></i>
                                <h6>Track Your Order</h6>
                                <p class="small text-muted mb-3">
                                    Monitor the progress of your project in real-time.
                                </p>
                                <a href="?page=dashboard" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>View Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-3x text-success mb-3"></i>
                                <h6>Download Invoice</h6>
                                <p class="small text-muted mb-3">
                                    Get a copy of your payment receipt for your records.
                                </p>
                                <a href="invoice.php?order=<?php echo $order_number; ?>" class="btn btn-success" target="_blank">
                                    <i class="fas fa-file-pdf me-2"></i>Download PDF
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Support -->
                <div class="card shadow mt-4">
                    <div class="card-body text-center">
                        <h6><i class="fas fa-headset me-2"></i>Need Help?</h6>
                        <p class="text-muted mb-3">
                            Our support team is here to help you with any questions or concerns.
                        </p>
                        <div class="row justify-content-center">
                            <div class="col-auto">
                                <a href="?page=contact" class="btn btn-outline-primary">
                                    <i class="fas fa-envelope me-2"></i>Contact Support
                                </a>
                            </div>
                            <div class="col-auto">
                                <a href="tel:+919876543210" class="btn btn-outline-success">
                                    <i class="fas fa-phone me-2"></i>Call Us
                                </a>
                            </div>
                            <div class="col-auto">
                                <a href="https://wa.me/919876543210" class="btn btn-outline-success" target="_blank">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Continue Shopping -->
                <div class="text-center mt-4">
                    <a href="?page=services" class="btn btn-outline-primary">
                        <i class="fas fa-shopping-cart me-2"></i>Browse More Services
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confetti Animation -->
    <canvas id="confetti-canvas"></canvas>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Confetti Animation Script -->
    <script>
    // Simple confetti animation
    (function() {
        const canvas = document.getElementById('confetti-canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        canvas.style.position = 'fixed';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '1000';
        
        const confetti = [];
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
        
        for (let i = 0; i < 100; i++) {
            confetti.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height - canvas.height,
                w: Math.random() * 10 + 5,
                h: Math.random() * 10 + 5,
                color: colors[Math.floor(Math.random() * colors.length)],
                speed: Math.random() * 3 + 2,
                angle: Math.random() * 360
            });
        }
        
        function updateConfetti() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            for (let i = 0; i < confetti.length; i++) {
                const c = confetti[i];
                
                ctx.save();
                ctx.translate(c.x + c.w / 2, c.y + c.h / 2);
                ctx.rotate(c.angle * Math.PI / 180);
                ctx.fillStyle = c.color;
                ctx.fillRect(-c.w / 2, -c.h / 2, c.w, c.h);
                ctx.restore();
                
                c.y += c.speed;
                c.angle += 2;
                
                if (c.y > canvas.height) {
                    c.y = -c.h;
                    c.x = Math.random() * canvas.width;
                }
            }
            
            requestAnimationFrame(updateConfetti);
        }
        
        updateConfetti();
        
        // Stop animation after 10 seconds
        setTimeout(() => {
            canvas.style.display = 'none';
        }, 10000);
    })();
    
    // Auto-redirect to dashboard after 30 seconds
    setTimeout(() => {
        if (confirm('Would you like to view your order dashboard?')) {
            window.location.href = '?page=dashboard';
        }
    }, 30000);
    </script>
    
    <style>
    .success-icon {
        animation: bounce 2s infinite;
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
    
    .step-icon {
        transition: transform 0.3s ease;
    }
    
    .step-icon:hover {
        transform: scale(1.1);
    }
    
    .card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }
    </style>
</body>
</html>
