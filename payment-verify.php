<?php
session_start();
require_once 'config/database.php';
require_once 'models/Order.php';
require_once 'includes/razorpay/RazorpayPayment.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!Session::isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

// Validate required fields
$required_fields = ['razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature', 'order_number'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty($input[$field])) {
        echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
        exit;
    }
}

$razorpay_order_id = $input['razorpay_order_id'];
$razorpay_payment_id = $input['razorpay_payment_id'];
$razorpay_signature = $input['razorpay_signature'];
$order_number = $input['order_number'];

try {
    // Get order details
    $order = new Order();
    $orderDetails = $order->getByOrderNumber($order_number);
    
    if (!$orderDetails) {
        echo json_encode(['success' => false, 'message' => 'Order not found']);
        exit;
    }
    
    // Check if order belongs to current user
    if ($orderDetails['user_id'] != Session::get('user_id')) {
        echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
        exit;
    }
    
    // Check if order is already paid
    if ($orderDetails['payment_status'] === 'paid') {
        echo json_encode(['success' => true, 'message' => 'Payment already verified']);
        exit;
    }
    
    // Initialize Razorpay
    $razorpay = new RazorpayPayment();
    
    // Verify payment signature
    $isValidSignature = $razorpay->verifyPaymentSignature(
        $razorpay_order_id,
        $razorpay_payment_id,
        $razorpay_signature
    );
    
    if (!$isValidSignature) {
        echo json_encode(['success' => false, 'message' => 'Invalid payment signature']);
        exit;
    }
    
    // Get payment details from Razorpay
    $paymentDetails = $razorpay->getPaymentDetails($razorpay_payment_id);
    
    if (!$paymentDetails) {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch payment details']);
        exit;
    }
    
    // Check payment status
    if ($paymentDetails['status'] !== 'captured') {
        echo json_encode(['success' => false, 'message' => 'Payment not captured']);
        exit;
    }
    
    // Verify amount
    $paidAmount = $paymentDetails['amount'] / 100; // Convert from paise
    if (abs($paidAmount - $orderDetails['total_amount']) > 0.01) {
        echo json_encode(['success' => false, 'message' => 'Amount mismatch']);
        exit;
    }
    
    // Database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Begin transaction
    $conn->beginTransaction();
    
    try {
        // Update order status
        $updateOrderQuery = "UPDATE orders SET 
                            payment_status = 'paid',
                            status = 'confirmed',
                            razorpay_payment_id = :payment_id,
                            paid_at = NOW()
                            WHERE id = :order_id";
        
        $updateOrderStmt = $conn->prepare($updateOrderQuery);
        $updateOrderStmt->bindParam(':payment_id', $razorpay_payment_id);
        $updateOrderStmt->bindParam(':order_id', $orderDetails['id']);
        
        if (!$updateOrderStmt->execute()) {
            throw new Exception('Failed to update order status');
        }
        
        // Log payment transaction
        $logTransactionQuery = "INSERT INTO payment_transactions 
                               (order_id, transaction_id, payment_gateway, amount, status, gateway_response, created_at)
                               VALUES (:order_id, :transaction_id, 'razorpay', :amount, 'success', :gateway_response, NOW())";
        
        $logTransactionStmt = $conn->prepare($logTransactionQuery);
        $logTransactionStmt->bindParam(':order_id', $orderDetails['id']);
        $logTransactionStmt->bindParam(':transaction_id', $razorpay_payment_id);
        $logTransactionStmt->bindParam(':amount', $paidAmount);
        $logTransactionStmt->bindParam(':gateway_response', json_encode($paymentDetails));
        
        if (!$logTransactionStmt->execute()) {
            throw new Exception('Failed to log transaction');
        }
        
        // Create initial order progress entry
        $progressQuery = "INSERT INTO order_progress 
                         (order_id, milestone, status, description, created_at)
                         VALUES (:order_id, 'Payment Received', 'completed', 'Payment successfully received and verified', NOW())";
        
        $progressStmt = $conn->prepare($progressQuery);
        $progressStmt->bindParam(':order_id', $orderDetails['id']);
        
        if (!$progressStmt->execute()) {
            throw new Exception('Failed to create progress entry');
        }
        
        // Send confirmation email (optional - implement if needed)
        // sendPaymentConfirmationEmail($orderDetails, $paymentDetails);
        
        // Commit transaction
        $conn->commit();
        
        // Log successful payment
        error_log("Payment verified successfully: Order #{$order_number}, Payment ID: {$razorpay_payment_id}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment verified successfully',
            'order_number' => $order_number,
            'payment_id' => $razorpay_payment_id
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        
        error_log("Payment verification failed: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    
} catch (Exception $e) {
    error_log("Payment verification error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Verification error: ' . $e->getMessage()]);
}

/**
 * Send payment confirmation email (implement if needed)
 */
function sendPaymentConfirmationEmail($orderDetails, $paymentDetails) {
    // Email implementation
    // You can use PHPMailer or similar library
    
    $to = $orderDetails['client_email'];
    $subject = "Payment Confirmation - Order #{$orderDetails['order_number']}";
    
    $message = "
    <html>
    <head>
        <title>Payment Confirmation</title>
    </head>
    <body>
        <h2>Payment Received Successfully!</h2>
        <p>Dear {$orderDetails['client_name']},</p>
        <p>We have successfully received your payment for order #{$orderDetails['order_number']}.</p>
        
        <h3>Order Details:</h3>
        <ul>
            <li><strong>Service:</strong> {$orderDetails['package_name']}</li>
            <li><strong>Amount:</strong> " . Utils::formatCurrency($orderDetails['total_amount']) . "</li>
            <li><strong>Payment ID:</strong> {$paymentDetails['id']}</li>
            <li><strong>Expected Delivery:</strong> " . date('M j, Y', strtotime($orderDetails['delivery_date'])) . "</li>
        </ul>
        
        <p>Our team will start working on your project shortly. You can track the progress in your dashboard.</p>
        
        <p>Thank you for choosing Website Developer 0002!</p>
        
        <p>Best regards,<br>Website Developer 0002 Team</p>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: <EMAIL>" . "\r\n";
    
    // Uncomment to send email
    // mail($to, $subject, $message, $headers);
}
?>
