<?php
session_start();
require_once 'config/database.php';
require_once 'models/User.php';
require_once 'models/Order.php';
require_once 'includes/razorpay/RazorpayPayment.php';

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Get order number from URL
$order_number = isset($_GET['order']) ? Utils::sanitizeInput($_GET['order']) : '';

if (!$order_number) {
    header('Location: index.php');
    exit;
}

// Get order details
$order = new Order();
$orderDetails = $order->getByOrderNumber($order_number);

if (!$orderDetails || $orderDetails['user_id'] != Session::get('user_id')) {
    header('Location: index.php');
    exit;
}

// Check if order is already paid
if ($orderDetails['payment_status'] === 'paid') {
    header('Location: payment-success.php?order=' . $order_number);
    exit;
}

// Get user details
$user = new User();
$userDetails = $user->getUserById($orderDetails['user_id']);

// Initialize Razorpay
$razorpay = new RazorpayPayment();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Website Developer 0002</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Payment Header -->
                <div class="text-center mb-4">
                    <h1 class="h3 mb-2">Complete Your Payment</h1>
                    <p class="text-muted">Order #<?php echo htmlspecialchars($orderDetails['order_number']); ?></p>
                </div>
                
                <!-- Order Summary Card -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><?php echo htmlspecialchars($orderDetails['package_name']); ?></h6>
                                <p class="text-muted mb-2"><?php echo htmlspecialchars($orderDetails['service_name']); ?></p>
                                
                                <div class="row">
                                    <div class="col-sm-6">
                                        <small class="text-muted">Customer:</small><br>
                                        <strong><?php echo htmlspecialchars($orderDetails['client_name']); ?></strong>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">Email:</small><br>
                                        <strong><?php echo htmlspecialchars($orderDetails['client_email']); ?></strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="mb-2">
                                    <small class="text-muted">Amount:</small><br>
                                    <h4 class="text-primary mb-0">
                                        <?php echo Utils::formatCurrency($orderDetails['total_amount']); ?>
                                    </h4>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Delivery: <?php echo date('M j, Y', strtotime($orderDetails['delivery_date'])); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Methods -->
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Method</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="payment-methods mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-shield-alt text-success me-2"></i>
                                        <span>Secure payment powered by Razorpay</span>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-6 col-md-3 mb-2">
                                            <div class="payment-option text-center p-2 border rounded">
                                                <i class="fas fa-credit-card fa-2x text-primary mb-1"></i>
                                                <small>Cards</small>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3 mb-2">
                                            <div class="payment-option text-center p-2 border rounded">
                                                <i class="fas fa-university fa-2x text-success mb-1"></i>
                                                <small>Net Banking</small>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3 mb-2">
                                            <div class="payment-option text-center p-2 border rounded">
                                                <i class="fas fa-mobile-alt fa-2x text-warning mb-1"></i>
                                                <small>UPI</small>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3 mb-2">
                                            <div class="payment-option text-center p-2 border rounded">
                                                <i class="fas fa-wallet fa-2x text-info mb-1"></i>
                                                <small>Wallets</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button id="payNowBtn" class="btn btn-primary btn-lg">
                                        <i class="fas fa-lock me-2"></i>
                                        Pay <?php echo Utils::formatCurrency($orderDetails['total_amount']); ?> Securely
                                    </button>
                                </div>
                                
                                <div class="text-center mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Your payment information is encrypted and secure
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="security-info">
                                    <h6><i class="fas fa-shield-alt text-success me-2"></i>Security Features</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-check text-success me-2"></i>256-bit SSL encryption</li>
                                        <li><i class="fas fa-check text-success me-2"></i>PCI DSS compliant</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Fraud protection</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Secure payment gateway</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Support -->
                <div class="text-center mt-4">
                    <p class="text-muted">
                        Need help? <a href="?page=contact">Contact our support team</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Processing Payment...</h5>
                    <p class="text-muted mb-0">Please wait while we verify your payment</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Razorpay Checkout -->
    <?php
    echo $razorpay->generatePaymentForm(
        $orderDetails['razorpay_order_id'],
        $orderDetails['total_amount'],
        'INR',
        [
            'name' => $userDetails['full_name'],
            'email' => $userDetails['email'],
            'phone' => $userDetails['phone']
        ],
        [
            'description' => $orderDetails['package_name']
        ]
    );
    ?>
    
    <script>
    document.getElementById('payNowBtn').addEventListener('click', function() {
        initiatePayment();
    });
    
    function handlePaymentSuccess(response) {
        // Show loading modal
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        // Send payment details to server for verification
        fetch("payment-verify.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
                order_number: '<?php echo $order_number; ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.hide();
            
            if (data.success) {
                window.location.href = "payment-success.php?order=<?php echo $order_number; ?>";
            } else {
                alert("Payment verification failed: " + data.message);
                window.location.reload();
            }
        })
        .catch(error => {
            loadingModal.hide();
            console.error("Error:", error);
            alert("An error occurred during payment verification. Please contact support.");
        });
    }
    
    function handlePaymentCancel() {
        console.log("Payment cancelled by user");
        // Optionally redirect or show a message
    }
    </script>
    
    <style>
    .payment-option {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .payment-option:hover {
        background-color: #f8f9fa;
        border-color: #007bff !important;
    }
    
    .security-info {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }
    </style>
</body>
</html>
